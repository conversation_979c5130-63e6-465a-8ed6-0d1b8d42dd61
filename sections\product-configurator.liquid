<!-- sections/product-configurator.liquid -->
<div class="product-configurator" role="region" aria-label="Product configurator">
  <div class="configurator-container">
    <div class="configurator-layout">
      <!-- Left: preview iframe / image -->
      <div class="configurator-preview" id="configurator-preview">
        <div class="renderease-iframe-container">
          <div class="configurator-embed" id="configurator-embed" aria-live="polite">
            <iframe
              id="main-configurator-iframe"
              src="{{ product.metafields.custom.default_iframe_url | default: section.settings.default_iframe }}"
              title="3D product configurator"
              frameborder="0"
              allow="accelerometer; autoplay; camera; gyroscope; microphone; clipboard-write; payment"
              loading="lazy"
              sandbox="allow-scripts allow-same-origin allow-forms"
            ></iframe>
          </div>
        </div>
      </div>

      <!-- Right: controls -->
      <aside class="configurator-controls" aria-label="Configurator controls">
        <!-- Breadcrumb -->
        <div class="breadcrumb-nav" aria-hidden="false">
          <span class="breadcrumb-item active">{{ section.settings.default_breadcrumb | default: "Rattan Colour" }}</span>
          <span class="breadcrumb-separator">›</span>
          <span class="breadcrumb-item">{{ section.settings.next_breadcrumb | default: "Upholstery Colour" }}</span>
        </div>

        <div class="product-header">
          <h2 class="product-name">{{ product.metafields.custom.product_subtitle | default: product.title }}</h2>
        </div>

        <div id="configurator-options" class="options-panel">
          {% comment %} Safe-guard and normalize materials list {% endcomment %}
          {%- assign raw_materials = product.metafields.custom.configurator_materials.value | default: '' -%}
          {%- if raw_materials == blank -%}
            {%- assign configurator_materials = [] -%}
          {%- else -%}
            {%- assign configurator_materials = raw_materials -%}
          {%- endif -%}

          {%- assign total_materials = configurator_materials.size -%}

          {% if total_materials > 0 %}
            <div class="option-category" data-category="materials">
              <div class="option-items" role="list">
                {% for material in configurator_materials %}
                  {% assign mname = material.material_name | default: 'Unknown' %}
                  {% assign miframe = material.iframe_url | default: section.settings.default_iframe %}
                  {% assign mcolor = material.fallback_color | default: '#8B7355' %}
                  {% assign mimage = material.material_image | default: '' %}
                  <div class="option-item{% if forloop.first %} selected{% endif %}" role="listitem"
                       tabindex="0"
                       data-iframe="{{ miframe }}"
                       data-image="{{ mimage }}"
                       data-material="{{ mname | handleize }}"
                       data-color="{{ mcolor }}">
                    
                    <div class="material-visual">
                      {% if mimage != '' %}
                        <img src="{{ mimage }}" alt="{{ mname }} swatch" width="40" height="40" class="swatch-image">
                      {% else %}
                        <div class="option-swatch" style="background: {{ mcolor }};" aria-hidden="true"></div>
                      {% endif %}
                    </div>

                    <div class="material-details">
                      <span class="material-name">{{ mname | upcase }}</span>
                    </div>

                    <div class="option-controls" role="group" aria-label="Material controls">
                      <button class="option-btn remove-btn" data-action="remove" aria-pressed="false" title="Deselect {{ mname }}">−</button>
                      <button class="option-btn add-btn{% if forloop.first %} active{% endif %}" data-action="add" aria-pressed="{% if forloop.first %}true{% else %}false{% endif %}" title="Select {{ mname }}">+</button>
                    </div>

                    {% if section.settings.debug_mode %}
                      <div class="material-debug" aria-hidden="true">
                        <div class="debug-row"><strong>{{ forloop.index }}.</strong> {{ mname }}</div>
                        <div class="debug-row">iframe: {{ miframe }}</div>
                        <div class="debug-row">color: {{ mcolor }}</div>
                      </div>
                    {% endif %}

                  </div>
                {% endfor %}
              </div>
            </div>
          {% else %}
            <div class="no-materials-message">
              <h4>❌ No Materials Detected</h4>
              <p>Total materials: {{ total_materials }}</p>
            </div>
          {% endif %}
        </div>
      </aside>
    </div>
  </div>
</div>

<style>
  /* Base layout matching the design */
  .product-configurator { 
    padding: 2rem 1rem; 
    background: #ffffff; 
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; 
    color: #333; 
    min-height: 100vh;
  }
  
  .configurator-container { 
    max-width: 1400px; 
    margin: 0 auto; 
    padding: 0 1rem; 
  }
  
  .configurator-layout { 
    display: grid; 
    grid-template-columns: 1fr 400px; 
    gap: 3rem; 
    align-items: start; 
  }

  /* Left side - 3D preview */
  .configurator-preview { 
    background: #f8f9fa; 
    border-radius: 16px; 
    padding: 2rem; 
    box-shadow: 0 4px 20px rgba(0,0,0,0.08); 
    border: 1px solid #e9ecef; 
    min-height: 500px; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
  }
  
  .renderease-iframe-container { 
    width: 100%; 
    height: 100%; 
    border-radius: 12px; 
    overflow: hidden; 
    background: #fff; 
    min-height: 450px;
  }
  
  .configurator-embed { 
    position: relative; 
    width: 100%; 
    height: 100%; 
    min-height: 450px; 
  }
  
  #main-configurator-iframe { 
    width: 100%; 
    height: 100%; 
    border: 0; 
    display: block; 
  }

  /* Loading state */
  .configurator-embed::before { 
    content: 'Loading 3D Configurator...'; 
    position: absolute; 
    inset: 0; 
    display: flex; 
    align-items: center; 
    justify-content: center; 
    color: #6c757d; 
    font-weight: 500; 
    z-index: 2; 
    background: #f8f9fa; 
    transition: opacity 0.3s ease; 
  }
  
  .configurator-embed.loaded::before { 
    opacity: 0; 
    pointer-events: none; 
  }

  /* Right side - controls */
  .configurator-controls { 
    padding: 1rem 0; 
  }

  /* Breadcrumb */
  .breadcrumb-nav { 
    font-size: 14px; 
    color: #6c757d; 
    margin-bottom: 1rem; 
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .breadcrumb-item.active { 
    color: #333; 
    font-weight: 500; 
  }
  
  .breadcrumb-separator { 
    color: #adb5bd; 
  }

  /* Product header */
  .product-header { 
    margin-bottom: 2rem; 
  }
  
  .product-name { 
    font-size: 28px; 
    margin: 0; 
    color: #333; 
    font-weight: 400; 
    line-height: 1.3;
  }

  /* Materials list */
  .option-category { 
    background: #fff; 
    border-radius: 12px; 
    overflow: hidden; 
  }

  .option-items { 
    display: flex; 
    flex-direction: column; 
  }

  .option-item { 
    padding: 1rem; 
    border-bottom: 1px solid #f1f3f4; 
    display: flex; 
    align-items: center; 
    gap: 1rem; 
    cursor: pointer; 
    transition: background-color 0.2s ease; 
    position: relative;
  }
  
  .option-item:last-child { 
    border-bottom: none; 
  }
  
  .option-item:hover { 
    background-color: #f8f9fa; 
  }
  
  .option-item.selected {
    background-color: #f8f9fa;
  }

  /* Material visual */
  .material-visual { 
    flex-shrink: 0; 
  }
  
  .option-swatch { 
    width: 40px; 
    height: 40px; 
    border-radius: 8px; 
    border: 2px solid #e9ecef; 
    box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
  }
  
  .swatch-image { 
    width: 40px; 
    height: 40px; 
    object-fit: cover; 
    border-radius: 8px; 
    border: 2px solid #e9ecef; 
    box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
  }

  /* Material details */
  .material-details { 
    flex: 1; 
  }
  
  .material-name { 
    font-size: 16px; 
    font-weight: 500; 
    color: #333; 
    display: block;
  }

  /* Control buttons */
  .option-controls { 
    display: flex; 
    gap: 0.5rem; 
    align-items: center; 
  }
  
  .option-btn { 
    width: 32px; 
    height: 32px; 
    border-radius: 50%; 
    border: 1px solid #dee2e6; 
    background: #fff; 
    display: inline-flex; 
    align-items: center; 
    justify-content: center; 
    cursor: pointer; 
    font-weight: 600; 
    font-size: 18px; 
    color: #6c757d;
    transition: all 0.2s ease; 
  }
  
  .option-btn:hover { 
    border-color: #007bff; 
    color: #007bff; 
  }
  
  .option-btn.active,
  .option-btn[aria-pressed="true"] { 
    background: #007bff; 
    color: #fff; 
    border-color: #007bff; 
  }
  
  .remove-btn:hover {
    border-color: #dc3545;
    color: #dc3545;
  }
  
  .remove-btn.active,
  .remove-btn[aria-pressed="true"] {
    background: #dc3545;
    color: #fff;
    border-color: #dc3545;
  }

  /* Debug mode */
  .material-debug { 
    font-family: monospace; 
    font-size: 11px; 
    color: #6c757d; 
    padding: 0.5rem; 
    background: #f8f9fa; 
    border-top: 1px dashed #dee2e6; 
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 10;
  }

  .no-materials-message { 
    padding: 2rem; 
    text-align: center; 
    color: #6c757d; 
    background: #f8f9fa; 
    border-radius: 8px; 
  }

  /* Responsive design */
  @media (max-width: 1024px) {
    .configurator-layout { 
      grid-template-columns: 1fr; 
      gap: 2rem;
    }
    
    .configurator-preview { 
      min-height: 400px; 
      order: 2;
    }
    
    .configurator-controls {
      order: 1;
    }
    
    .configurator-embed { 
      min-height: 350px; 
    }
  }

  @media (max-width: 768px) {
    .product-configurator {
      padding: 1rem 0.5rem;
    }
    
    .configurator-container {
      padding: 0 0.5rem;
    }
    
    .configurator-layout {
      gap: 1.5rem;
    }
    
    .configurator-preview {
      padding: 1rem;
      min-height: 300px;
    }
    
    .configurator-embed {
      min-height: 250px;
    }
    
    .product-name {
      font-size: 24px;
    }
    
    .option-item {
      padding: 0.75rem;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .option-item,
    .option-btn,
    .configurator-embed::before {
      transition: none !important;
    }
  }
</style>

<script>
/* Enhanced configurator with proper material selection */
document.addEventListener('DOMContentLoaded', function() {
  const container = document.querySelector('.product-configurator');
  if (!container) return;
  if (container.dataset.configuratorInit === '1') return; // avoid double init
  container.dataset.configuratorInit = '1';

  const mainIframe = container.querySelector('#main-configurator-iframe');
  const configuratorEmbed = container.querySelector('#configurator-embed');

  // Safely injected default iframe URL from Liquid (json-encoded)
  const defaultIframeUrl = {{ product.metafields.custom.default_iframe_url | default: section.settings.default_iframe | json }};

  // Basic host whitelist — add trusted hosts here if needed
  let allowedHosts = ['config.renderease.com'];
  try {
    allowedHosts.push(new URL(defaultIframeUrl).host);
  } catch (e) { /* ignore invalid default url */ }

  function isValidIframeUrl(url) {
    try {
      const u = new URL(url, window.location.href);
      return allowedHosts.indexOf(u.host) !== -1;
    } catch (e) {
      return false;
    }
  }

  function setIframeSrc(url) {
    let final = url;
    if (!final || !isValidIframeUrl(final)) final = defaultIframeUrl;
    if (mainIframe.src === final) return;
    configuratorEmbed.classList.remove('loaded'); // show loading text
    mainIframe.src = final;
  }

  function selectMaterial(optionItem) {
    // Remove selection from all items
    container.querySelectorAll('.option-item').forEach(item => {
      item.classList.remove('selected');
      item.querySelectorAll('.option-btn').forEach(btn => {
        btn.classList.remove('active');
        btn.setAttribute('aria-pressed', 'false');
      });
    });

    // Select current item
    optionItem.classList.add('selected');
    const addBtn = optionItem.querySelector('.add-btn');
    if (addBtn) {
      addBtn.classList.add('active');
      addBtn.setAttribute('aria-pressed', 'true');
    }

    // Update iframe
    const iframeUrl = optionItem.dataset.iframe;
    const materialName = optionItem.querySelector('.material-name')?.textContent?.trim() || 'Material';
    setIframeSrc(iframeUrl);
    console.log('Selected material:', materialName, iframeUrl);
  }

  function deselectMaterial(optionItem) {
    optionItem.classList.remove('selected');
    optionItem.querySelectorAll('.option-btn').forEach(btn => {
      btn.classList.remove('active');
      btn.setAttribute('aria-pressed', 'false');
    });

    const materialName = optionItem.querySelector('.material-name')?.textContent?.trim() || 'Material';
    setIframeSrc(defaultIframeUrl);
    console.log('Deselected material:', materialName);
  }

  // Handle button clicks
  container.addEventListener('click', function(e) {
    const btn = e.target.closest('.option-btn');
    if (!btn) return;

    e.preventDefault();
    e.stopPropagation();

    const optionItem = btn.closest('.option-item');
    if (!optionItem) return;

    const action = btn.dataset.action;

    if (action === 'add') {
      selectMaterial(optionItem);
    } else if (action === 'remove') {
      deselectMaterial(optionItem);
    }
  });

  // Handle row clicks (select material)
  container.addEventListener('click', function(e) {
    const optionItem = e.target.closest('.option-item');
    if (!optionItem) return;

    // Don't trigger if clicking on buttons
    if (e.target.closest('.option-btn')) return;

    e.preventDefault();
    selectMaterial(optionItem);
  });

  // Keyboard navigation
  container.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
      const btn = e.target.closest('.option-btn');
      if (btn) {
        e.preventDefault();
        btn.click();
        return;
      }

      const row = e.target.closest('.option-item');
      if (row) {
        e.preventDefault();
        selectMaterial(row);
      }
    }
  });

  // Handle iframe load
  if (mainIframe) {
    mainIframe.addEventListener('load', function() {
      configuratorEmbed.classList.add('loaded');
      console.log('Configurator loaded:', mainIframe.src);
    });

    // Trigger load event if iframe is already loaded
    if (mainIframe.complete) {
      configuratorEmbed.classList.add('loaded');
    }
  }

  // Initialize first material as selected if none are selected
  const firstItem = container.querySelector('.option-item');
  if (firstItem && !container.querySelector('.option-item.selected')) {
    selectMaterial(firstItem);
  }
});
</script>

{% schema %}
{
  "name": "Product Configurator",
  "settings": [
    {
      "type": "text",
      "id": "default_iframe",
      "label": "Default Configurator URL",
      "default": "https://config.renderease.com/configurator/?model=OS-SS008-DEMO"
    },
    {
      "type": "text",
      "id": "default_breadcrumb",
      "label": "Default Breadcrumb Text",
      "default": "Rattan Colour"
    },
    {
      "type": "text",
      "id": "next_breadcrumb",
      "label": "Next Breadcrumb Text",
      "default": "Upholstery Colour"
    },
    {
      "type": "checkbox",
      "id": "debug_mode",
      "label": "Show debug rows",
      "default": false
    }
  ],
  "presets": [{ "name": "Product Configurator (Stacked Materials)" }]
}
{% endschema %}
